/** @type {import('next').NextConfig} */
const nextConfig = {
  // Re-enable TypeScript and ESLint checking for better code quality
  eslint: {
    ignoreDuringBuilds: false,
  },
  typescript: {
    ignoreBuildErrors: false,
  },

  // Optimize images
  images: {
    unoptimized: false,
    formats: ['image/webp', 'image/avif'],
    deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
    imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
  },

  // Enable experimental features for better performance
  experimental: {
    optimizeCss: true,
    // Enable Turbopack for development (faster compilation)
    turbo: {
      // Turbopack configuration for better performance
      rules: {
        // Optimize specific file types
        '*.svg': {
          loaders: ['@svgr/webpack'],
          as: '*.js',
        },
      },
      resolveAlias: {
        // Add aliases for better module resolution
        '@': './src',
        '@/components': './components',
        '@/lib': './lib',
        '@/app': './app',
      },
    },
    // Optimize package imports for better tree shaking
    optimizePackageImports: [
      '@radix-ui/react-accordion',
      '@radix-ui/react-alert-dialog',
      '@radix-ui/react-avatar',
      '@radix-ui/react-checkbox',
      '@radix-ui/react-dialog',
      '@radix-ui/react-dropdown-menu',
      '@radix-ui/react-label',
      '@radix-ui/react-popover',
      '@radix-ui/react-select',
      '@radix-ui/react-separator',
      '@radix-ui/react-slider',
      '@radix-ui/react-switch',
      '@radix-ui/react-tabs',
      '@radix-ui/react-toast',
      '@radix-ui/react-tooltip',
      'lucide-react',
      'recharts',
      'date-fns',
      'clsx',
    ],

  },

  // Enable server components optimization
  serverExternalPackages: ['@radix-ui'],

  // Webpack optimizations (fallback when not using Turbopack)
  webpack: (config, { dev, isServer, webpack }) => {
    // Configure webpack cache to prevent corruption
    if (dev) {
      const path = require('path');
      config.cache = {
        type: 'filesystem',
        version: '1.0',
        cacheDirectory: path.resolve(process.cwd(), '.next/cache/webpack'),
        store: 'pack',
        buildDependencies: {
          config: [__filename],
        },
        // Add cache invalidation for better reliability
        name: `${isServer ? 'server' : 'client'}-${dev ? 'development' : 'production'}-${Date.now()}`,
      };

      // Optimize module resolution for development
      config.resolve.symlinks = false;
      config.resolve.cacheWithContext = false;
    }

    // Optimize bundle size for production
    if (!dev && !isServer) {
      config.optimization.splitChunks = {
        chunks: 'all',
        minSize: 20000,
        maxSize: 244000,
        cacheGroups: {
          vendor: {
            test: /[\\/]node_modules[\\/]/,
            name: 'vendors',
            chunks: 'all',
            priority: 10,
          },
          radix: {
            test: /[\\/]node_modules[\\/]@radix-ui[\\/]/,
            name: 'radix',
            chunks: 'all',
            priority: 20,
          },
          charts: {
            test: /[\\/]node_modules[\\/](recharts|d3-)[\\/]/,
            name: 'charts',
            chunks: 'all',
            priority: 20,
          },
          common: {
            name: 'common',
            minChunks: 2,
            chunks: 'all',
            priority: 5,
            reuseExistingChunk: true,
          },
        },
      };
    }

    // Reduce bundle size by excluding source maps in production
    if (!dev) {
      config.devtool = false;
    }

    // Add performance optimizations
    config.optimization.moduleIds = 'deterministic';
    config.optimization.chunkIds = 'deterministic';

    return config;
  },

  // Compiler optimizations
  compiler: {
    removeConsole: process.env.NODE_ENV === 'production',
  },

  // Output optimization
  output: 'standalone',

  // Performance optimizations
  poweredByHeader: false,
  reactStrictMode: true,
  // swcMinify is enabled by default in Next.js 13+
}

export default nextConfig